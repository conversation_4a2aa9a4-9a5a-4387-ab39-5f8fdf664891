import { useState } from 'react'
import axios from 'axios'

const ScrapingForm = ({ onResult, onStart, isLoading }) => {
  const [url, setUrl] = useState('')
  const [scraper, setScraper] = useState('metascraper')
  const [elements, setElements] = useState([])
  const [customSelectors, setCustomSelectors] = useState([])
  const [waitTime, setWaitTime] = useState(5000)
  const [retries, setRetries] = useState(3)
  const [customElements, setCustomElements] = useState('')

  const availableElements = [
    { id: 'title', label: 'Title' },
    { id: 'description', label: 'Description' },
    { id: 'images', label: 'Images' },
    { id: 'price', label: 'Price' },
    { id: 'old_price', label: 'Old Price' },
    { id: 'author', label: 'Author' }
  ]

  const scraperOptions = [
    {
      id: 'metascraper',
      label: 'Metascraper',
      description: 'Spezialisiert auf Metadaten-Extraktion',
      details: 'Extrahiert automatisch Titel, Beschreibungen, Bilder und andere Metadaten aus HTML-Meta-Tags. Ideal für schnelle Übersichten und SEO-Daten.'
    },
    {
      id: 'crawlee',
      label: 'Crawlee',
      description: 'JavaScript-gerenderte Inhalte',
      details: 'Modernes Web-Scraping-Framework mit Playwright-Integration. Perfekt für Single-Page-Applications und dynamische Inhalte, die JavaScript benötigen.'
    },
    {
      id: 'cheerio',
      label: 'Cheerio',
      description: 'Schnelles HTML-Parsing',
      details: 'Server-seitiges jQuery für Node.js. Sehr schnell und effizient für statische HTML-Inhalte. Unterstützt benutzerdefinierte CSS-Selektoren.'
    },
    {
      id: 'stealth-playwright',
      label: 'Stealth Playwright',
      description: '🛡️ Anti-Detection Browser-Automatisierung',
      details: 'Erweiterte Browser-Automatisierung mit Anti-Detection-Features. Umgeht Bot-Schutz-Systeme durch realistische Browser-Simulation und Stealth-Techniken.'
    },
    {
      id: 'stealth-puppeteer',
      label: 'Stealth Puppeteer',
      description: '🥷 Erweiterte Cloudflare-Umgehung',
      details: 'Hochentwickelte Anti-Bot-Detection mit Puppeteer-Extra und Stealth-Plugin. Simuliert menschliches Verhalten, umgeht Cloudflare-Challenges und andere Bot-Schutz-Systeme.'
    }
  ]

  const handleElementChange = (elementId) => {
    setElements(prev => 
      prev.includes(elementId) 
        ? prev.filter(id => id !== elementId)
        : [...prev, elementId]
    )
  }

  const addCustomSelector = () => {
    setCustomSelectors(prev => [...prev, { name: '', selector: '', attribute: '' }])
  }

  const updateCustomSelector = (index, field, value) => {
    setCustomSelectors(prev => 
      prev.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    )
  }

  const removeCustomSelector = (index) => {
    setCustomSelectors(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!url) {
      alert('Please enter a URL')
      return
    }

    onStart()

    try {
      const endpoint = `http://localhost:3001/api/scrape/${scraper}`

      // Combine selected elements with custom elements
      const allElements = [...elements]
      if (customElements.trim()) {
        const customElementsList = customElements.split(',').map(el => el.trim()).filter(el => el)
        allElements.push(...customElementsList)
      }

      const payload = {
        url,
        elements: allElements.length > 0 ? allElements : null,
        customSelectors: customSelectors.filter(cs => cs.name && cs.selector),
        waitTime: parseInt(waitTime),
        retries: parseInt(retries)
      }

      const response = await axios.post(endpoint, payload)
      onResult(response.data)
    } catch (error) {
      console.error('Scraping error:', error)
      onResult({
        success: false,
        error: error.response?.data?.error || 'Failed to scrape URL',
        message: error.response?.data?.message || error.message
      })
    }
  }

  return (
    <div className="scraping-form">
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="url">URL to scrape:</label>
          <input
            type="url"
            id="url"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://example.com"
            required
            disabled={isLoading}
          />
        </div>

        <div className="form-group">
          <label>Scraping Library:</label>
          <div className="scraper-options">
            {scraperOptions.map(option => (
              <label key={option.id} className="radio-option">
                <input
                  type="radio"
                  name="scraper"
                  value={option.id}
                  checked={scraper === option.id}
                  onChange={(e) => setScraper(e.target.value)}
                  disabled={isLoading}
                />
                <div className="radio-content">
                  <div className="radio-header">
                    <strong>{option.label}</strong>
                    <span className="radio-description">{option.description}</span>
                  </div>
                  <div className="radio-details">
                    {option.details}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>

        <div className="form-group">
          <label>Elements to extract:</label>
          <div className="element-checkboxes">
            {availableElements.map(element => (
              <label key={element.id} className="checkbox-option">
                <input
                  type="checkbox"
                  checked={elements.includes(element.id)}
                  onChange={() => handleElementChange(element.id)}
                  disabled={isLoading}
                />
                {element.label}
              </label>
            ))}
          </div>
          <small>Leave empty to extract all available data</small>
        </div>

        <div className="form-group">
          <label htmlFor="customElements">Custom Elements:</label>
          <input
            type="text"
            id="customElements"
            value={customElements}
            onChange={(e) => setCustomElements(e.target.value)}
            placeholder="e.g. brand, category, rating, discount"
            disabled={isLoading}
          />
          <small>Enter additional elements to extract, separated by commas</small>
        </div>

        {scraper === 'cheerio' && (
          <div className="form-group">
            <label>Custom CSS Selectors:</label>
            {customSelectors.map((selector, index) => (
              <div key={index} className="custom-selector">
                <input
                  type="text"
                  placeholder="Field name"
                  value={selector.name}
                  onChange={(e) => updateCustomSelector(index, 'name', e.target.value)}
                  disabled={isLoading}
                />
                <input
                  type="text"
                  placeholder="CSS selector"
                  value={selector.selector}
                  onChange={(e) => updateCustomSelector(index, 'selector', e.target.value)}
                  disabled={isLoading}
                />
                <input
                  type="text"
                  placeholder="Attribute (optional)"
                  value={selector.attribute}
                  onChange={(e) => updateCustomSelector(index, 'attribute', e.target.value)}
                  disabled={isLoading}
                />
                <button 
                  type="button" 
                  onClick={() => removeCustomSelector(index)}
                  disabled={isLoading}
                  className="remove-btn"
                >
                  ✕
                </button>
              </div>
            ))}
            <button 
              type="button" 
              onClick={addCustomSelector}
              disabled={isLoading}
              className="add-selector-btn"
            >
              + Add Custom Selector
            </button>
          </div>
        )}

        {(scraper === 'stealth-playwright' || scraper === 'stealth-puppeteer') && (
          <div className="form-group">
            <label>Advanced Settings:</label>
            <div className="advanced-settings">
              <div className="setting-item">
                <label htmlFor="waitTime">Wait Time (ms):</label>
                <input
                  type="number"
                  id="waitTime"
                  value={waitTime}
                  onChange={(e) => setWaitTime(e.target.value)}
                  min="1000"
                  max="30000"
                  step="1000"
                  disabled={isLoading}
                />
                <small>Time to wait for dynamic content to load</small>
              </div>
              <div className="setting-item">
                <label htmlFor="retries">Retries:</label>
                <input
                  type="number"
                  id="retries"
                  value={retries}
                  onChange={(e) => setRetries(e.target.value)}
                  min="1"
                  max="5"
                  disabled={isLoading}
                />
                <small>Number of retry attempts if scraping fails</small>
              </div>
            </div>
          </div>
        )}

        <button type="submit" disabled={isLoading} className="submit-btn">
          {isLoading ? 'Scraping...' : 'Start Scraping'}
        </button>
      </form>
    </div>
  )
}

export default ScrapingForm
