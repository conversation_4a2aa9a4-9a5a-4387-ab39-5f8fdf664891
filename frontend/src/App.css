/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #fafafa;
  color: #333;
  line-height: 1.6;
}

/* App Layout */
.app {
  min-height: 100vh;
  background-color: #fafafa;
}

/* Header Styles */
.app-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 24px;
}

.header-title h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.header-title p {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Tab Navigation */
.tab-navigation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  gap: 0;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  background: none;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.tab:hover:not(.disabled) {
  color: #374151;
  background-color: #f9fafb;
}

.tab.active {
  color: #111827;
  border-bottom-color: #f97316;
}

.tab.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tab-path {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: #9ca3af;
}

.tab-badge {
  background: #f97316;
  color: white;
  font-size: 0.625rem;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

/* Loading Animation */
.loading {
  text-align: center;
  color: #6b7280;
  margin: 40px 0;
  background: white;
  padding: 40px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.spinner {
  border: 4px solid #f3f4f6;
  border-radius: 50%;
  border-top: 4px solid #f97316;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scraping Form */
.scraping-form {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

/* URL Section */
.url-section {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.url-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.url-input-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.url-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s;
}

.url-input:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.url-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
}

.url-actions {
  display: flex;
  gap: 8px;
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid;
}

.btn-primary {
  background: #f97316;
  color: white;
  border-color: #f97316;
}

.btn-primary:hover:not(:disabled) {
  background: #ea580c;
  border-color: #ea580c;
}

.btn-primary:disabled {
  background: #d1d5db;
  border-color: #d1d5db;
  cursor: not-allowed;
}

.btn-secondary {
  background: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-icon {
  font-size: 0.75rem;
}

/* Options Section */
.options-section {
  padding: 16px 24px;
}

.options-toggle {
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.2s;
}

.options-toggle:hover {
  color: #374151;
}

.advanced-options {
  border-top: 1px solid #e5e7eb;
  padding: 24px;
}

/* Status Section */
.status-section {
  padding: 16px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  border-radius: 0 0 8px 8px;
}

.status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.status-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #374151;
}

.status-indicator {
  font-size: 0.75rem;
}

.status-indicator.streaming {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-actions {
  display: flex;
  gap: 8px;
}

.status-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  color: #374151;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.status-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.status-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.status-btn.error {
  background: #1f2937;
  color: white;
  border-color: #1f2937;
}

.status-btn.error:hover:not(:disabled) {
  background: #111827;
  border-color: #111827;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input[type="url"] {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input[type="url"]:focus {
  outline: none;
  border-color: #667eea;
}

.scraper-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.radio-option {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  min-height: 120px;
}

.radio-option:hover {
  border-color: #667eea;
  background-color: #f8f9ff;
}

.radio-option input[type="radio"] {
  margin-right: 12px;
  margin-top: 2px;
}

.radio-option input[type="radio"]:checked + .radio-content {
  color: #667eea;
}

.radio-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.radio-header {
  margin-bottom: 8px;
}

.radio-header strong {
  display: block;
  margin-bottom: 4px;
  font-size: 16px;
  color: #333;
}

.radio-description {
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.radio-details {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.element-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.checkbox-option:hover {
  background-color: #f8f9ff;
  border-color: #667eea;
}

.checkbox-option input[type="checkbox"] {
  margin-right: 8px;
}

.custom-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.custom-selector input {
  flex: 1;
  padding: 8px;
  border: 1px solid #e1e5e9;
  border-radius: 4px;
}

.remove-btn {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.remove-btn:hover {
  background: #ff3742;
}

.add-selector-btn {
  background: #5dade2;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-selector-btn:hover {
  background: #3498db;
}

.submit-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s;
  width: 100%;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Result Display */
.result-display {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.result-header h2 {
  margin: 0;
  color: #333;
}

.result-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.view-toggle {
  display: flex;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  overflow: hidden;
}

.view-toggle button {
  padding: 8px 16px;
  border: none;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.view-toggle button.active {
  background: #667eea;
  color: white;
}

.action-btn {
  padding: 8px 16px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.action-btn:hover {
  background: #f8f9ff;
  border-color: #667eea;
}

.error-result {
  background: #ffe6e6;
  border: 1px solid #ff9999;
  border-radius: 8px;
  padding: 20px;
  color: #cc0000;
}

.result-meta {
  background: #f8f9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.meta-item {
  font-size: 14px;
}

.meta-item strong {
  color: #667eea;
}

.formatted-result {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.raw-json {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  overflow-x: auto;
  white-space: pre-wrap;
  margin-bottom: 20px;
}

.data-summary {
  background: #f0f8ff;
  border-radius: 8px;
  padding: 20px;
}

.data-summary h3 {
  margin-top: 0;
  color: #333;
}

.data-summary ul {
  list-style: none;
  padding: 0;
}

.data-summary li {
  padding: 8px 0;
  border-bottom: 1px solid #e1e5e9;
}

.data-summary li:last-child {
  border-bottom: none;
}

/* JSON Display Styles */
.json-display {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
}

.json-item {
  margin: 4px 0;
}

.json-key {
  color: #0066cc;
  font-weight: bold;
  margin-right: 8px;
}

.json-string {
  color: #008000;
}

.json-number {
  color: #ff6600;
}

.json-boolean {
  color: #cc0066;
}

.json-null {
  color: #999999;
  font-style: italic;
}

.json-link {
  color: #0066cc;
  text-decoration: underline;
}

.json-link:hover {
  background-color: #f0f8ff;
}

.json-toggle {
  cursor: pointer;
  user-select: none;
  color: #666;
  font-weight: bold;
}

.json-toggle:hover {
  background-color: #f0f0f0;
  border-radius: 3px;
}

.json-content {
  border-left: 2px solid #e1e5e9;
  padding-left: 10px;
  margin-left: 10px;
}

.json-array, .json-object {
  margin: 2px 0;
}

/* Advanced Settings */
.advanced-settings {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
  padding: 20px;
  background: #f8f9ff;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.setting-item {
  display: flex;
  flex-direction: column;
}

.setting-item label {
  font-weight: 600;
  margin-bottom: 5px;
  color: #333;
}

.setting-item input[type="number"] {
  padding: 8px 12px;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.setting-item input[type="number"]:focus {
  outline: none;
  border-color: #667eea;
}

.setting-item small {
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

/* Image Gallery Styles */
.image-gallery {
  margin: 20px 0;
  padding: 15px;
  background: #f8f9ff;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.image-gallery h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.image-item {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-item img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.delete-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  z-index: 10;
}

.delete-image-btn:hover {
  background: rgba(255, 0, 0, 1);
}

.image-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120px;
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  background: #fff5f5;
  color: #cc0000;
  font-size: 11px;
  text-align: center;
  padding: 10px;
}

.image-error .image-url {
  color: #0066cc;
  text-decoration: underline;
  margin-top: 5px;
  font-size: 10px;
}

.no-images {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Editable Field Styles */
.editable-field {
  margin-bottom: 20px;
  padding: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: border-color 0.3s;
}

.editable-field:hover {
  border-color: #667eea;
}

.field-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.view-mode {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.view-mode:hover {
  background: #f8f9ff;
  border-radius: 4px;
  padding: 8px;
  margin: -8px;
}

.field-value {
  flex: 1;
  color: #333;
  line-height: 1.4;
}

.field-value em {
  color: #999;
}

.edit-hint {
  opacity: 0;
  transition: opacity 0.2s;
  color: #667eea;
  font-size: 14px;
}

.view-mode:hover .edit-hint {
  opacity: 1;
}

.edit-mode {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.edit-input {
  padding: 10px;
  border: 2px solid #667eea;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 40px;
}

.edit-input:focus {
  outline: none;
  border-color: #4c63d2;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.edit-actions {
  display: flex;
  gap: 10px;
}

.save-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.cancel-btn {
  background: #dc3545;
  color: white;
}

.cancel-btn:hover {
  background: #c82333;
}

/* Results Layout */
.results-layout {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  align-items: start;
}

.main-content {
  min-width: 0; /* Prevent grid overflow */
}

.images-sidebar {
  position: sticky;
  top: 20px;
}

.editable-fields {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }

  .header-content {
    padding: 16px 20px;
  }

  .tab-navigation {
    padding: 0 20px;
  }

  .tab {
    padding: 10px 12px;
    font-size: 0.8rem;
  }

  .url-input-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .url-actions {
    justify-content: center;
  }

  .status-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .status-actions {
    justify-content: center;
  }

  .scraper-options {
    grid-template-columns: 1fr;
  }

  .result-header {
    flex-direction: column;
    align-items: stretch;
  }

  .result-actions {
    justify-content: center;
  }

  .custom-selector {
    flex-direction: column;
    gap: 5px;
  }

  /* Mobile responsive for new components */
  .results-layout {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .images-sidebar {
    position: static;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }

  .image-item img {
    height: 100px;
  }

  .editable-field {
    padding: 12px;
  }

  .edit-actions {
    flex-direction: column;
  }

  .save-btn, .cancel-btn {
    width: 100%;
  }
}
