const express = require('express');
const metascraper = require('metascraper')([
  require('metascraper-author')(),
  require('metascraper-date')(),
  require('metascraper-description')(),
  require('metascraper-image')(),
  require('metascraper-logo')(),
  require('metascraper-clearbit')(),
  require('metascraper-publisher')(),
  require('metascraper-title')(),
  require('metascraper-url')()
]);
const axios = require('axios');

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { url, elements } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Fetch the HTML content with enhanced headers
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'de-DE,de;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
      },
      timeout: 15000,
      maxRedirects: 5,
      validateStatus: function (status) {
        return status < 500; // Accept 4xx errors to handle them properly
      }
    });

    const html = response.data;
    const metadata = await metascraper({ html, url });

    // Filter results based on requested elements
    let filteredResults = {};
    
    if (!elements || elements.length === 0) {
      // Return all available metadata if no specific elements requested
      filteredResults = metadata;
    } else {
      // Map common element names to metascraper fields
      const elementMapping = {
        'title': 'title',
        'description': 'description',
        'image': 'image',
        'images': 'image', // metascraper returns single image
        'author': 'author',
        'date': 'date',
        'publisher': 'publisher',
        'logo': 'logo',
        'url': 'url',
        'price': 'price',
        'old_price': 'old_price'
      };

      elements.forEach(element => {
        const field = elementMapping[element.toLowerCase()];
        if (field && metadata[field]) {
          filteredResults[element] = metadata[field];
        }
      });
    }

    // Add some additional extracted data
    const result = {
      url: url,
      scraper: 'metascraper',
      timestamp: new Date().toISOString(),
      data: filteredResults,
      success: true
    };

    res.json(result);

  } catch (error) {
    console.error('Metascraper error:', error);

    // Check if it's a Cloudflare protection error
    if (error.response && error.response.status === 403) {
      res.status(403).json({
        error: 'Website is protected by Cloudflare',
        message: 'This website blocks simple HTTP requests. Please use Stealth Playwright or Stealth Puppeteer for Cloudflare-protected sites.',
        recommendation: 'Use "Stealth Playwright" scraper instead',
        success: false,
        cloudflareDetected: true
      });
    } else {
      res.status(500).json({
        error: 'Failed to scrape URL with metascraper',
        message: error.message,
        success: false
      });
    }
  }
});

module.exports = router;
