// Library imports with their specific use cases:

// Express.js - Web framework for Node.js
// Einsatzzweck: Erstellt HTTP-Router für REST-API-Endpunkte, ermöglicht die Verarbeitung von POST-Requests
// und die Rückgabe von JSON-Responses für das Web-Scraping-Interface
const express = require('express');

// Puppeteer-Extra - Enhanced version of Google's Puppeteer
// Einsatzzweck: Erweiterte Browser-Automatisierung mit Plugin-System, ermöglicht das Laden von
// Anti-Detection-Plugins und bietet bessere Kontrolle über Browser-Instanzen als Standard-Puppeteer
const puppeteer = require('puppeteer-extra');

// Puppeteer-Extra Stealth Plugin - Anti-detection plugin for Puppeteer
// Einsatzzweck: Umgeht Bot-Detection-Systeme wie Cloudflare, Distil Networks und andere Anti-Bot-Maßnahmen
// durch das Entfernen von Automatisierungs-Indikatoren und das Simulieren echter Browser-Eigenschaften
const StealthPlugin = require('puppeteer-extra-plugin-stealth');

// User-Agents - Library for generating realistic user agent strings
// Einsatzzweck: Generiert zufällige, realistische User-Agent-Strings verschiedener Browser und Betriebssysteme
// um die Erkennung als automatisierter Bot zu erschweren und natürliches Nutzerverhalten zu simulieren
const UserAgent = require('user-agents');

// Add stealth plugin
puppeteer.use(StealthPlugin());

const router = express.Router();

// Enhanced browser configuration
const getBrowserConfig = () => {
  const userAgent = new UserAgent();

  return {
    headless: 'new', // Verwende neuen headless Modus
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--disable-gpu',
      '--window-size=1920,1080',
      '--start-maximized',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--disable-field-trial-config',
      '--disable-back-forward-cache',
      '--disable-features=TranslateUI',
      '--disable-ipc-flooding-protection',
      '--disable-blink-features=AutomationControlled',
      '--exclude-switches=enable-automation',
      '--disable-extensions-except=/path/to/extension',
      '--disable-plugins-discovery',
      '--allow-running-insecure-content',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor,VizHitTestSurfaceLayer',
      '--disable-features=VizServiceDisplayCompositor'
    ],
    defaultViewport: { width: 1920, height: 1080 },
    userAgent: userAgent.toString(),
    // WebSocket error prevention
    handleSIGINT: false,
    handleSIGTERM: false,
    handleSIGHUP: false,
    ignoreDefaultArgs: ['--enable-automation'],
    // Timeout settings to prevent hanging
    timeout: 30000
  };
};

// Human-like delays
const randomDelay = (min = 1000, max = 3000) => {
  return new Promise(resolve => {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    setTimeout(resolve, delay);
  });
};

// Simulate human mouse movements
const simulateHumanActivity = async (page) => {
  // Random mouse movements
  for (let i = 0; i < 5; i++) {
    await page.mouse.move(
      Math.random() * 1920,
      Math.random() * 1080,
      { steps: Math.floor(Math.random() * 10) + 5 }
    );
    await randomDelay(100, 300);
  }
  
  // Random scrolling
  await page.evaluate(() => {
    const scrollAmount = Math.random() * 1000;
    window.scrollTo({
      top: scrollAmount,
      behavior: 'smooth'
    });
  });
  
  await randomDelay(500, 1500);
};

// Advanced stealth setup
const setupAdvancedStealth = async (page) => {
  // Remove automation indicators
  await page.evaluateOnNewDocument(() => {
    // Remove webdriver property
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined,
    });
    
    // Remove automation flags
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
    delete window.cdc_adoQpoasnfa76pfcZLmcfl_Proxy;
    
    // Override chrome runtime
    window.chrome = {
      runtime: {}
    };
    
    // Override permissions
    const originalQuery = window.navigator.permissions.query;
    window.navigator.permissions.query = (parameters) => (
      parameters.name === 'notifications' ?
        Promise.resolve({ state: Notification.permission }) :
        originalQuery(parameters)
    );
    
    // Override plugins
    Object.defineProperty(navigator, 'plugins', {
      get: () => [
        {
          0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format", enabledPlugin: Plugin},
          description: "Portable Document Format",
          filename: "internal-pdf-viewer",
          length: 1,
          name: "Chrome PDF Plugin"
        }
      ],
    });
    
    // Override languages
    Object.defineProperty(navigator, 'languages', {
      get: () => ['de-DE', 'de', 'en-US', 'en'],
    });
    
    // Override platform
    Object.defineProperty(navigator, 'platform', {
      get: () => 'MacIntel',
    });
  });
  
  // Set realistic headers
  await page.setExtraHTTPHeaders({
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Encoding': 'gzip, deflate, br',
    'Accept-Language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7',
    'Cache-Control': 'max-age=0',
    'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Upgrade-Insecure-Requests': '1',
    'DNT': '1'
  });
  
  // Set timezone and locale
  await page.emulateTimezone('Europe/Berlin');
  await page.setGeolocation({ latitude: 52.520008, longitude: 13.404954 });
};

router.post('/', async (req, res) => {
  let browser = null;
  let page = null;
  let scrapedData = null;
  let responseAlreadySent = false;

  // Function to safely send response
  const sendResponse = (data) => {
    if (!responseAlreadySent && !res.headersSent) {
      responseAlreadySent = true;
      res.json(data);
    }
  };

  // Function to safely send error
  const sendError = (statusCode, errorData) => {
    if (!responseAlreadySent && !res.headersSent) {
      responseAlreadySent = true;
      res.status(statusCode).json(errorData);
    }
  };

  try {
    const { url, elements, waitTime = 5000, retries = 3 } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    console.log(`🚀 Starting stealth Puppeteer scraping for: ${url}`);
    
    // Launch browser with stealth configuration
    const config = getBrowserConfig();
    browser = await puppeteer.launch(config);
    page = await browser.newPage();
    
    // Setup advanced stealth measures
    await setupAdvancedStealth(page);
    
    // Set viewport
    await page.setViewport({ width: 1920, height: 1080 });
    
    // Navigation with retry logic
    let attempt = 0;
    let navigationSuccess = false;
    
    while (attempt < retries && !navigationSuccess) {
      try {
        console.log(`📡 Attempt ${attempt + 1} to load page...`);
        
        // Random delay before navigation
        await randomDelay(2000, 4000);
        
        // Navigate to page
        const response = await page.goto(url, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });
        
        if (response && response.status() === 200) {
          navigationSuccess = true;
          console.log('✅ Page loaded successfully');
        } else {
          console.log(`⚠️ Received status: ${response ? response.status() : 'unknown'}`);
        }
        
      } catch (error) {
        console.log(`❌ Navigation attempt ${attempt + 1} failed:`, error.message);
        attempt++;
        
        if (attempt < retries) {
          await randomDelay(3000, 6000);
        }
      }
    }
    
    if (!navigationSuccess) {
      throw new Error('Failed to load page after all retries');
    }
    
    // Wait and simulate human behavior
    await randomDelay(2000, 4000);
    await simulateHumanActivity(page);
    
    // Check for Cloudflare challenge
    const isChallengePage = await page.evaluate(() => {
      return document.title.includes('Just a moment') || 
             document.body.textContent.includes('Checking your browser') ||
             document.querySelector('#challenge-error-text') !== null ||
             document.querySelector('.cf-browser-verification') !== null;
    });
    
    if (isChallengePage) {
      console.log('🛡️ Cloudflare challenge detected, waiting for completion...');
      
      // Wait for challenge to complete
      try {
        await page.waitForFunction(() => {
          return !document.title.includes('Just a moment') && 
                 !document.body.textContent.includes('Checking your browser') &&
                 !document.querySelector('#challenge-error-text');
        }, { timeout: 30000 });
        
        console.log('✅ Challenge completed successfully');
        await randomDelay(2000, 4000);
        
      } catch (error) {
        console.log('⚠️ Challenge timeout, attempting to proceed...');
      }
    }
    
    // Additional wait for dynamic content
    await page.waitForTimeout(waitTime);
    
    // Extract data
    scrapedData = await page.evaluate((requestedElements) => {
      const result = {};
      
      // Helper functions
      const getTextContent = (selector) => {
        const element = document.querySelector(selector);
        return element ? element.textContent.trim() : null;
      };
      
      const getAttribute = (selector, attribute) => {
        const element = document.querySelector(selector);
        return element ? element.getAttribute(attribute) : null;
      };
      
      const getAllElements = (selector, attribute = null) => {
        const elements = document.querySelectorAll(selector);
        return Array.from(elements).map(el => 
          attribute ? el.getAttribute(attribute) : el.textContent.trim()
        ).filter(item => item && item.length > 0);
      };
      
      // Enhanced selectors for e-commerce sites
      const titleSelectors = [
        'h1', '.product-title', '.title', '[data-testid="title"]',
        '.product-name', '.item-title', '.product-header h1'
      ];
      
      const priceSelectors = [
        '.price', '.product-price', '.price-current', '.price-now',
        '[data-testid="price"]', '[data-price]', '.cost', '.amount',
        '.price-value', '.sale-price', '.current-price'
      ];

      const oldPriceSelectors = [
        '.old-price', '.original-price', '.was-price', '.regular-price', 'price-base', 'previous-price',
        '.price-old', '.price-before', '.crossed-price', '.strikethrough-price',
        '[data-testid="old-price"]', '[class*="old-price"]', '[class*="original-price"]',
        '[class*="was-price"]', 'del', 's', '.line-through', '.price-strike'
      ];
      
      const imageSelectors = [
        '.product-image img', '.item-image img', '.gallery img',
        '[data-testid*="image"] img', '.product-photo img'
      ];
      
      // Extract title
      if (!requestedElements || requestedElements.includes('title')) {
        for (const selector of titleSelectors) {
          const title = getTextContent(selector);
          if (title) {
            result.title = title;
            break;
          }
        }
        if (!result.title) {
          result.title = getTextContent('title');
        }
      }
      
      // Extract description
      if (!requestedElements || requestedElements.includes('description')) {
        result.description = getAttribute('meta[name="description"]', 'content') ||
                           getAttribute('meta[property="og:description"]', 'content') ||
                           getTextContent('.description') ||
                           getTextContent('.product-description') ||
                           getTextContent('[data-testid="description"]');
      }
      
      // Extract images
      if (!requestedElements || requestedElements.includes('images') || requestedElements.includes('image')) {
        const images = [];
        
        // Get og:image
        const ogImage = getAttribute('meta[property="og:image"]', 'content');
        if (ogImage) images.push(ogImage);
        
        // Get product images
        for (const selector of imageSelectors) {
          const imgs = getAllElements(selector, 'src');
          images.push(...imgs);
        }
        
        // Filter and clean images
        result.images = [...new Set(images)]
          .filter(src => src && !src.includes('data:') && src.length > 10)
          .slice(0, 10);
      }
      
      // Enhanced price extraction with intelligent parsing
      if (!requestedElements || requestedElements.includes('price')) {
        for (const selector of priceSelectors) {
          const priceElement = document.querySelector(selector);
          if (priceElement) {
            const priceText = priceElement.textContent.trim();
            if (priceText && /[\d.,]+/.test(priceText)) {
              // Parse multiple prices from the same element
              const prices = priceText.match(/[\d.,]+\s*€/g);
              if (prices && prices.length >= 2) {
                // First price is usually the current price, second is old price
                result.price = prices[0].trim();
                result.old_price = prices[1].trim();
              } else {
                result.price = priceText;
              }
              break;
            }
          }
        }

        // Try data attributes
        if (!result.price) {
          const priceElement = document.querySelector('[data-price]');
          if (priceElement) {
            result.price = priceElement.getAttribute('data-price');
          }
        }
      }

      // Extract old prices (only if not already found in price parsing)
      if ((!requestedElements || requestedElements.includes('old_price')) && !result.old_price) {
        for (const selector of oldPriceSelectors) {
          const oldPriceElement = document.querySelector(selector);
          if (oldPriceElement) {
            const oldPriceText = oldPriceElement.textContent.trim();
            if (oldPriceText && /[\d.,]+/.test(oldPriceText)) {
              result.old_price = oldPriceText;
              break;
            }
          }
        }

        // Try data attributes for old price
        if (!result.old_price) {
          const oldPriceElement = document.querySelector('[data-old-price]');
          if (oldPriceElement) {
            result.old_price = oldPriceElement.getAttribute('data-old-price');
          }
        }
      }
      
      // Extract author/brand
      if (!requestedElements || requestedElements.includes('author')) {
        result.author = getAttribute('meta[name="author"]', 'content') ||
                      getTextContent('.brand') ||
                      getTextContent('.manufacturer') ||
                      getTextContent('.author');
      }
      
      return result;
    }, elements);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'debug-puppeteer.png', fullPage: false });

    const result = {
      url: url,
      scraper: 'stealth-puppeteer',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true,
      challengeDetected: isChallengePage
    };

    console.log('✅ Stealth Puppeteer scraping completed');

    // Send response first, then close browser asynchronously to avoid WebSocket errors
    sendResponse(result);

    // Close browser asynchronously after response is sent
    setImmediate(async () => {
      try {
        if (page) {
          await page.close();
          console.log('✅ Page closed successfully');
        }
      } catch (error) {
        console.log('⚠️ Error closing page:', error.message);
      }

      try {
        if (browser) {
          await browser.close();
          console.log('✅ Browser closed successfully');
        }
      } catch (error) {
        console.log('⚠️ Error closing browser:', error.message);
      }
    });
    
  } catch (error) {
    console.error('❌ Stealth Puppeteer error:', error);

    // Check if it's a WebSocket error (ErrorEvent or Error with socket hang up)
    const isWebSocketError = (
      (error.message && error.message.includes('socket hang up')) ||
      (error[Symbol.for('kError')] && error[Symbol.for('kError')].code === 'ECONNRESET') ||
      (error.code === 'ECONNRESET') ||
      (error.type === 'error' && error.target && error.target._url && error.target._url.includes('devtools'))
    );

    if (isWebSocketError && scrapedData) {
      console.log('⚠️ WebSocket error detected, but scraping was successful. Returning data...');

      const result = {
        url: url,
        scraper: 'stealth-puppeteer',
        timestamp: new Date().toISOString(),
        data: scrapedData,
        success: true,
        warning: 'WebSocket connection error during cleanup, but scraping completed successfully'
      };

      return sendResponse(result);
    }

    sendError(500, {
      error: 'Failed to scrape URL with Stealth Puppeteer',
      message: error.message || 'Unknown error occurred',
      success: false
    });
  } finally {
    // Only close if there was an error (success case closes asynchronously)
    if (page && !res.headersSent) {
      try {
        await page.close();
        console.log('✅ Page closed in finally block');
      } catch (error) {
        console.log('⚠️ Error closing page in finally:', error.message);
      }
    }

    if (browser && !res.headersSent) {
      try {
        await browser.close();
        console.log('✅ Browser closed in finally block');
      } catch (error) {
        console.log('⚠️ Error closing browser in finally:', error.message);
      }
    }
  }
});

module.exports = router;
