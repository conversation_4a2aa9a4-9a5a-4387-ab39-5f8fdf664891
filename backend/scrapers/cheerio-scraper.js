const express = require('express');
const cheerio = require('cheerio');
const axios = require('axios');

const router = express.Router();

router.post('/', async (req, res) => {
  try {
    const { url, elements, customSelectors } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    // Fetch the HTML content
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000
    });

    const html = response.data;
    const $ = cheerio.load(html);

    let scrapedData = {};

    // Helper function to try multiple selectors
    const trySelectors = (selectors) => {
      for (const selector of selectors) {
        const element = $(selector);
        if (element.length > 0) {
          return element;
        }
      }
      return null;
    };

    // Only extract elements that are specifically requested
    if (elements && elements.includes('title')) {
      const titleSelectors = [
        'title',
        'h1',
        '[data-testid="title"]',
        '.title',
        '.product-title',
        '.entry-title'
      ];
      const titleElement = trySelectors(titleSelectors);
      scrapedData.title = titleElement ? titleElement.first().text().trim() : null;
    }

    // Extract description
    if (elements && elements.includes('description')) {
      const description = $('meta[name="description"]').attr('content') ||
                         $('meta[property="og:description"]').attr('content') ||
                         $('.description').first().text().trim() ||
                         $('[data-testid="description"]').first().text().trim();
      scrapedData.description = description;
    }

    // Extract images
    if (elements && (elements.includes('images') || elements.includes('image'))) {
      const images = [];
      
      // Get og:image
      const ogImage = $('meta[property="og:image"]').attr('content');
      if (ogImage) images.push(ogImage);
      
      // Get all img src attributes
      $('img').each((i, elem) => {
        const src = $(elem).attr('src');
        if (src) {
          // Convert relative URLs to absolute
          const absoluteUrl = src.startsWith('http') ? src : new URL(src, url).href;
          images.push(absoluteUrl);
        }
      });
      
      scrapedData.images = [...new Set(images)]; // Remove duplicates
    }

    // Enhanced price extraction with intelligent parsing
    if (elements && elements.includes('price')) {
      const priceSelectors = [
        '.price',
        '[data-testid="price"]',
        '.cost',
        '.amount',
        '[class*="price"]',
        '[id*="price"]',
        '.currency',
        '.product-price'
      ];

      for (const selector of priceSelectors) {
        const priceElement = $(selector);
        if (priceElement.length > 0) {
          const priceText = priceElement.first().text().trim();
          if (priceText && /[\d.,]+/.test(priceText)) {
            // Parse multiple prices from the same element
            const prices = priceText.match(/[\d.,]+\s*€/g);
            if (prices && prices.length >= 2) {
              // First price is usually the current price, second is old price
              scrapedData.price = prices[0].trim();
              scrapedData.old_price = prices[1].trim();
            } else {
              scrapedData.price = priceText;
            }
            break;
          }
        }
      }
    }

    // Extract old prices (only if not already found in price parsing)
    if (elements && elements.includes('old_price') && !scrapedData.old_price) {
      const oldPriceSelectors = [
        '.old-price',
        '.original-price',
        '.was-price',
        '.regular-price',
        '.price-old',
        '.price-before',
        '.crossed-price',
        '.strikethrough-price',
        '[data-testid="old-price"]',
        '[class*="old-price"]',
        '[class*="original-price"]',
        '[class*="was-price"]',
        'del',
        's',
        '.line-through',
        '.price-strike'
      ];

      for (const selector of oldPriceSelectors) {
        const oldPriceElement = $(selector);
        if (oldPriceElement.length > 0) {
          const oldPriceText = oldPriceElement.first().text().trim();
          if (oldPriceText && /[\d.,]+/.test(oldPriceText)) {
            scrapedData.old_price = oldPriceText;
            break;
          }
        }
      }
    }

    // Extract author
    if (elements && elements.includes('author')) {
      const author = $('meta[name="author"]').attr('content') ||
                    $('.author').first().text().trim() ||
                    $('[data-testid="author"]').first().text().trim() ||
                    $('.byline').first().text().trim();
      scrapedData.author = author;
    }

    // Extract custom selectors if provided
    if (customSelectors && Array.isArray(customSelectors)) {
      customSelectors.forEach(({ name, selector, attribute }) => {
        try {
          const element = $(selector);
          if (element.length > 0) {
            if (attribute) {
              scrapedData[name] = element.first().attr(attribute);
            } else {
              scrapedData[name] = element.first().text().trim();
            }
          }
        } catch (error) {
          console.error(`Error with custom selector ${name}:`, error);
        }
      });
    }

    const result = {
      url: url,
      scraper: 'cheerio',
      timestamp: new Date().toISOString(),
      data: scrapedData,
      success: true
    };

    res.json(result);

  } catch (error) {
    console.error('Cheerio scraper error:', error);

    // Handle specific error types
    if (error.response && error.response.status === 403) {
      return res.status(403).json({
        error: 'Access forbidden - Website blocked the request',
        message: 'The website has blocked this request. This is common with Cloudflare-protected sites. Try using Stealth Playwright or Stealth Puppeteer instead.',
        success: false,
        suggestion: 'Use Stealth Playwright or Stealth Puppeteer for Cloudflare-protected sites'
      });
    }

    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return res.status(400).json({
        error: 'Cannot reach the website',
        message: 'The website could not be reached. Please check the URL.',
        success: false
      });
    }

    res.status(500).json({
      error: 'Failed to scrape URL with Cheerio',
      message: error.message,
      success: false
    });
  }
});

module.exports = router;
