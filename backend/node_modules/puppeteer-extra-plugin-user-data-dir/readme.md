# puppeteer-extra-plugin-user-data-dir

> A plugin for [puppeteer-extra](https://github.com/berstend/puppeteer-extra).

### Install

```bash
yarn add puppeteer-extra-plugin-user-data-dir
```

## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

-   [Plugin](#plugin)

### [Plugin](https://github.com/berstend/puppeteer-extra/blob/db57ea66cf10d407cf63af387892492e495a84f2/packages/puppeteer-extra-plugin-user-data-dir/index.js#L19-L113)

**Extends: PuppeteerExtraPlugin**

Further reading:
<https://chromium.googlesource.com/chromium/src/+/master/docs/user_data_dir.md>

Type: `function (opts)`

-   `opts`   (optional, default `{}`)

* * *
