{"name": "puppeteer-extra-plugin-user-data-dir", "version": "2.4.1", "description": "Custom user data directory for puppeteer.", "main": "index.js", "repository": "berstend/puppeteer-extra", "author": "be<PERSON><PERSON>", "license": "MIT", "scripts": {"docs": "node -e 0", "lint": "eslint --ext .js .", "test": "run-p lint", "test-ci": "run-s test"}, "engines": {"node": ">=8"}, "keywords": ["puppeteer", "puppeteer-extra", "puppeteer-extra-plugin", "user-data", "userDataDir", "profile", "chrome", "headless", "pupeteer"], "devDependencies": {"ava": "2.4.0", "npm-run-all": "^4.1.5", "puppeteer": "^2.0.0"}, "dependencies": {"debug": "^4.1.1", "fs-extra": "^10.0.0", "puppeteer-extra-plugin": "^3.2.3", "rimraf": "^3.0.2"}, "peerDependencies": {"playwright-extra": "*", "puppeteer-extra": "*"}, "peerDependenciesMeta": {"puppeteer-extra": {"optional": true}, "playwright-extra": {"optional": true}}, "gitHead": "2f4a357f233b35a7a20f16ce007f5ef3f62765b9"}