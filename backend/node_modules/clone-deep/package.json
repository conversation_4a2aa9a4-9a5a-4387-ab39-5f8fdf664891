{"name": "clone-deep", "description": "Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.", "version": "0.2.4", "homepage": "https://github.com/jonschlinkert/clone-deep", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/clone-deep", "bugs": {"url": "https://github.com/jonschlinkert/clone-deep/issues"}, "license": "MIT", "files": ["index.js", "utils.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"for-own": "^0.1.3", "is-plain-object": "^2.0.1", "kind-of": "^3.0.2", "lazy-cache": "^1.0.3", "shallow-clone": "^0.1.2"}, "devDependencies": {"mocha": "*", "should": "*"}, "keywords": ["array", "clone", "clone-array", "clone-array-deep", "clone-date", "clone-deep", "clone-object", "clone-object-deep", "clone-reg-exp", "date", "deep", "exp", "for", "for-in", "for-own", "javascript", "mixin", "mixin-object", "object", "own", "reg", "util", "utility"], "verb": {"related": {"list": []}, "plugins": ["gulp-format-md"]}}